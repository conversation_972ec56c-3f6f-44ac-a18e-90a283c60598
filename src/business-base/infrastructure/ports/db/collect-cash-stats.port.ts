import { DbCommonPort } from '@common/db/ports/common.port';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';

export interface CollectCashStatsPort extends DbCommonPort<CollectCashStatsEntity> {
  getTotalDealValueByCustomerIdWithDateRange(
    customerId: string,
    startDate?: string,
    endDate?: string,
  ): Promise<number>;
  getTotalDealValueByPortfolioIdWithDateRange(
    portfolioId: string,
    startDate?: string,
    endDate?: string,
  ): Promise<number>;
  getAverageTicketByCustomerIdWithDateRange(
    customerId: string,
    startDate?: string,
    endDate?: string,
  ): Promise<number>;
  getAverageTicketByPortfolioIdWithDateRange(
    portfolioId: string,
    startDate?: string,
    endDate?: string,
  ): Promise<number>;
}
