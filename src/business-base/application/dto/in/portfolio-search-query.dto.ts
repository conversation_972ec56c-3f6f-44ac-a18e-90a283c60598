import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsN<PERSON>ber, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class PortfolioSearchQueryDto {
  @ApiProperty({
    description: 'Search portfolios by name (case-insensitive partial match)',
    example: 'test portfolio',
    required: false,
  })
  @IsString({ message: 'name must be a string' })
  @IsOptional()
  readonly name?: string;

  @ApiProperty({
    description: 'Page number for pagination (starts from 1)',
    example: 1,
    minimum: 1,
    required: false,
    default: 1,
  })
  @Type(() => Number)
  @IsNumber({}, { message: 'page must be a number' })
  @Min(1, { message: 'page must be at least 1' })
  @IsOptional()
  readonly page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
    default: 10,
  })
  @Type(() => Number)
  @IsNumber({}, { message: 'limit must be a number' })
  @Min(1, { message: 'limit must be at least 1' })
  @Max(100, { message: 'limit cannot exceed 100' })
  @IsOptional()
  readonly limit?: number = 10;

  @ApiProperty({
    description: 'Start date for deal value filtering (ISO 8601 format)',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  readonly startDate?: string

  @ApiProperty({
    description: 'End date for deal value filtering (ISO 8601 format)',
    example: '2024-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  readonly endDate?: string
}
