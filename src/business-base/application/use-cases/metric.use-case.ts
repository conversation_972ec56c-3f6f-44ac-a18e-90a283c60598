import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { GroupByDate, PortfolioItemStatus } from '@common/enums';
import { Inject } from '@nestjs/common';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';

export class MetricUseCase {
  constructor(
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,

    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,

    private readonly portfolioItemUseCase: PortfolioItemUseCase,
  ) {}

  async getPortfolioCreatedMetrics(
    customerId: string,
    dateStart: string,
    dateEnd: string,
  ): Promise<any> {

    const startDate = new Date(dateStart).toISOString();
    const endDate = new Date(dateEnd).toISOString();

    const portfolios = await this.portfolioAdapter.count({
      customerId: customerId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    });

    const metrics = {
      portfoliosCreated: portfolios,
      startDate,
      endDate
    };

    return metrics;
  }

  async getPortfolioItemCreatedMetrics(
    customerId: string,
    dateStart: string,
    dateEnd: string,
  ): Promise<any> {
    const startDate = new Date(dateStart).toISOString();
    const endDate = new Date(dateEnd).toISOString();

    const customerPortfolios = await this.portfolioAdapter.getAll({
      customerId: customerId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    });

    const customerPortfoliosIds = customerPortfolios.map(portfolio => portfolio.id);

    const portfolioItems = await this.portfolioItemAdapter.count({
      portfolioId: { in: customerPortfoliosIds },
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    });

    const metrics = {
      portfolioItemsCreated: portfolioItems,
      startDate: startDate,
      endDate: endDate,
    };

    return metrics;
  }

  async getPortfolioItemsCountByDateFilteredByStatus(
    customerId: string,
    startDate: string,
    endDate: string,
    GroupByDate: GroupByDate,
    currentStatus: PortfolioItemStatus,
  ): Promise<any> {
    const portfolioItems =
      await this.portfolioItemUseCase.getPortfolioItemsCountByDateFilteredByStatus(
        customerId,
        startDate,
        endDate,
        GroupByDate,
        currentStatus,
      );
    return portfolioItems;
  }

  async getPortfolioItemsWithInteractionCountByDate(
    customerId: string,
    startDate: string,
    endDate: string,
    GroupByDate: GroupByDate,
  ): Promise<any> {
    const portfolioItems =
      await this.portfolioItemUseCase.getPortfolioItemsWithInteractionCountByDate(
        customerId,
        startDate,
        endDate,
        GroupByDate,
      );
    return portfolioItems;
  }

  async getPortfolioItemsWithOnlyAiInteractionCountByDate(
    customerId: string,
    startDate: string,
    endDate: string
  ): Promise<number> {
    const portfolioItemsCount =
      await this.portfolioItemUseCase.getPortfolioItemsWithAiOnlyInteractionCountByDate(
        customerId,
        startDate,
        endDate,
      );
    return portfolioItemsCount;
  }
}
