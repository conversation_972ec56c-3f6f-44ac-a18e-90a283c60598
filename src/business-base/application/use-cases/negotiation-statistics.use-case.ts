import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import {
  NegotiationStatisticsResponseDto,
  PortfolioNegotiationStatsDto,
  NegotiationStatisticsSummaryDto,
} from '@business-base/application/dto/out/negotiation-statistics-response.dto';
import { RecordStatus } from '@common/enums';

@Injectable()
export class NegotiationStatisticsUseCase {
  constructor(
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,
  ) { }

  async getNegotiationStatistics(
    customerId: string,
    portfolioId: string,
    dateStart: string,
    dateEnd: string,
  ): Promise<NegotiationStatisticsResponseDto> {
    const portfolio = await this.portfolioAdapter.get(portfolioId);
    if (!portfolio || portfolio.customerId !== customerId || portfolio.status !== RecordStatus.ACTIVE) {
      throw new NotFoundException(`Portfolio with ID ${portfolioId} not found or does not belong to customer`);
    }

    const startDate = new Date(dateStart).toISOString();
    const endDate = new Date(dateEnd).toISOString();
    const portfolioStats = await this.portfolioItemAdapter.getNegotiationStatistics(
      customerId,
      portfolioId,
      startDate,
      endDate,
    );

    const portfolioStatsDto = new PortfolioNegotiationStatsDto(
      portfolioStats.portfolioId,
      portfolioStats.activeNegotiations,
      portfolioStats.inProgressNegotiations,
    );

    const summaryDto = new NegotiationStatisticsSummaryDto(
      portfolioStats.activeNegotiations,
      portfolioStats.inProgressNegotiations,
    );

    return new NegotiationStatisticsResponseDto([portfolioStatsDto], summaryDto);
  }
}
