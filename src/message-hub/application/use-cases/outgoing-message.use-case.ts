import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { SendMessageRequestDto } from '@message-hub/application/dto/in/send-message-request.dto';
import { OutgoingMessagePort } from '@message-hub/infrastructure/ports/db/outgoing-message.port';
import {
  CommunicationChannel,
  MessageType,
  PhoneVerificationStatus,
  RecordStatus,
} from '@common/enums';
import { InfraWhatsappSelfhostedPort } from '@message-hub/infrastructure/ports/http/whatsapp-selfhosted.port';
import { CustomerPhonePort } from '@message-hub/infrastructure/ports/db/customer-phone.port';
import { CustomerPhoneDestinationPort } from '@message-hub/infrastructure/ports/db/customer-phone-destination.port';
import { NumberService } from '@common/utils/number-service';
import { CustomerPhoneEntity } from '@message-hub/domain/entities/customer-phone.entity';
import { CustomerPhoneDestinationEntity } from '@message-hub/domain/entities/customer-phone-destination.entity';
import { SmsServiceProviderPort } from '@message-hub/infrastructure/ports/http/sms-service-provider.port';
import { RetrievePendingMessageRequestDto } from '@message-hub/application/dto/in/retrieve-pending-message-request.dto';
import { plainToClass } from 'class-transformer';
import { OutgoingMessageEntity } from '@message-hub/domain/entities/outgoing-message.entity';
import { RetrievePendingMessageResponseDto } from '@message-hub/application/dto/out/retrieve-pending-message-response.dto';
import { CustomerPhoneUseCase } from '@message-hub/application/use-cases/customer-phone.use-case';
import { InfraLovelacePort } from '@message-hub/infrastructure/ports/http/lovelace.port';
import { MessageUseCase } from '@message-hub/application/use-cases/message.use-case';
import { InfraWhatsAppApiPort } from '@message-hub/infrastructure/ports/http/whatsapp-api.port';
import { DefaultOutgoingMessage } from '@message-hub/application/models/default-message.models';
import { PhoneVerificationPort } from '@message-hub/infrastructure/ports/db/phone-verification.port';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { PhoneVerificationUseCase } from '@message-hub/application/use-cases/phone-verification.use-case';
import { InfraBusinessBasePort } from '@message-hub/infrastructure/ports/http/business-base.port';

@Injectable()
export class OutgoingMessageUseCase {
  constructor(
    @Inject('OutgoingMessagePort')
    private readonly outgoingMessageAdapter: OutgoingMessagePort,
    @Inject('InfraWhatsappSelfhostedPort')
    private readonly infraWhatsappSelfhostedAdapter: InfraWhatsappSelfhostedPort,
    @Inject('InfraLovelacePort')
    private readonly infraLovelaceAdapter: InfraLovelacePort,
    private readonly numberService: NumberService,
    @Inject('CustomerPhonePort')
    private readonly customerPhoneAdapter: CustomerPhonePort,
    @Inject('CustomerPhoneDestinationPort')
    private readonly customerPhoneDestinationAdapter: CustomerPhoneDestinationPort,
    @Inject('SmsServiceProviderPort')
    readonly smsServiceProviderPort: SmsServiceProviderPort,
    @Inject(forwardRef(() => CustomerPhoneUseCase))
    readonly customerPhoneUseCase: CustomerPhoneUseCase,
    @Inject('InfraWhatsAppApiPort')
    private readonly infraWhatsappApiAdapter: InfraWhatsAppApiPort,
    @Inject(forwardRef(() => MessageUseCase))
    readonly messageUseCase: MessageUseCase,
    @Inject('PhoneVerificationPort')
    readonly phoneVerificationAdapter: PhoneVerificationPort,
    @Inject()
    readonly phoneVerificationUseCase: PhoneVerificationUseCase,
    @Inject('InfraBusinessBasePort')
    readonly businessBaseAdapter: InfraBusinessBasePort,
  ) {}

  async sendPendingMessage(fromNumber: string, channel: CommunicationChannel): Promise<void> {
    try {
      await this.outgoingMessageAdapter.processOutgoingMessage(
        fromNumber,
        channel,
        this.infraWhatsappSelfhostedAdapter.sendMessage.bind(this.infraWhatsappSelfhostedAdapter),
        this.infraWhatsappSelfhostedAdapter.sendMessageWithFile.bind(
          this.infraWhatsappSelfhostedAdapter,
        ),
        this.smsServiceProviderPort.sendSms.bind(this.smsServiceProviderPort),
        this.infraWhatsappApiAdapter.sendMessage.bind(this.infraWhatsappApiAdapter),
      );
    } catch (error) {
      logger.error(
        `Error sending outgoing message from phone: ${fromNumber} and channel: ${channel}. Error: ${JSON.stringify(
          error,
        )}`,
      );
    }
  }

  async sendMessageLovelaceDirectly(
    customerId: string,
    channel: CommunicationChannel,
    to: string,
    message: string,
  ): Promise<void> {
    const customerPhones = await this.customerPhoneAdapter.getAll({
      customerId,
      communicationChannel: channel,
    });

    if (!customerPhones || customerPhones.length === 0) {
      logger.error(`No customer phone found for customer: ${customerId} and channel: ${channel}`);
      return;
    }

    const customerPhoneEntity = customerPhones[0];

    try {
      await this.infraLovelaceAdapter.sendMessage(
        customerPhoneEntity.phoneNumber,
        to,
        message,
        customerPhoneEntity.apiUrl,
      );
    } catch (error) {
      await this.outgoingMessageAdapter.insertOutgoingMessage(
        {
          customerId,
          from: customerPhoneEntity.phoneNumber,
          to,
          messageType: MessageType.TEXT,
          message,
          channel,
          status: RecordStatus.ACTIVE,
          isFirstMessage: false,
          apiUrl: customerPhoneEntity.apiUrl,
        },
        0,
      );

      logger.error(
        `OutgoingMessageUseCase - SendMessageWithoutDelay: error sending message to ${to} CustomerId: ${customerId} - Channel: ${channel} - Message: ${message} - Error: ${JSON.stringify(
          error,
        )}`,
      );
    }
  }

  async sendMessageWhatsappApiDirectly(
    sendMessageRequestDto: SendMessageRequestDto,
    messageOutgoing: DefaultOutgoingMessage,
  ): Promise<void> {
    const channel = sendMessageRequestDto.communicationChannel;
    const customerPhones = await this.customerPhoneAdapter.getAll({
      customerId: sendMessageRequestDto.customerId,
      communicationChannel: channel,
    });

    logger.info(
      `sendMessageWhatsappApiDirectly - CustomerPhones: ${JSON.stringify(
        customerPhones[0],
      )} - SendMessageRequestDto: ${JSON.stringify(
        sendMessageRequestDto,
      )} - MessageOutgoing: ${JSON.stringify(messageOutgoing)}`,
    );

    if (!customerPhones || customerPhones.length === 0) {
      logger.error(
        `No customer phone found for customer: ${sendMessageRequestDto.customerId} and channel: ${channel}`,
      );
      return;
    }

    const customerPhoneEntity = customerPhones[0];

    try {
      if (channel === CommunicationChannel.WHATSAPP_API) {
        logger.info('sendMessageWhatsappApiDirectly - Sending message to whatsapp api directly');
        await this.infraWhatsappApiAdapter.sendMessage(customerPhoneEntity, messageOutgoing);
      }
    } catch (error) {
      await this.outgoingMessageAdapter.insertOutgoingMessage(
        {
          customerId: sendMessageRequestDto.customerId,
          from: customerPhoneEntity.phoneNumber,
          to: sendMessageRequestDto.to,
          messageType: sendMessageRequestDto.messageType,
          message: messageOutgoing?.message,
          channel: channel,
          status: RecordStatus.ACTIVE,
          isFirstMessage: false,
          apiUrl: customerPhoneEntity.apiUrl,
          fileUrl: sendMessageRequestDto?.fileUrl,
        },
        0,
      );

      logger.error(
        `OutgoingMessageUseCase - SendMessageWhatsappApiDirectly: error sending message to ${
          sendMessageRequestDto.to
        } CustomerId: ${sendMessageRequestDto.customerId} - Channel: ${channel} - Message: ${
          messageOutgoing.message
        } - Error: ${JSON.stringify(error)}`,
      );
    }
  }

  async getPendingOutgoingMessages(
    customerId: string,
    pendingMessageRequestDto: RetrievePendingMessageRequestDto,
  ): Promise<RetrievePendingMessageResponseDto[]> {
    logger.info(
      `Processing pending outgoing messages for customer: ${customerId}. Request: ${JSON.stringify(
        pendingMessageRequestDto,
      )}`,
    );

    const pendingMessages = await this.outgoingMessageAdapter.getPendingOutgoingMessage(
      customerId,
      pendingMessageRequestDto.communicationChannel,
      pendingMessageRequestDto.to,
    );

    logger.info(
      `Returning pending messages to customer: ${customerId}. Messages: ${JSON.stringify(
        pendingMessages,
      )}`,
    );

    return pendingMessages.map(this.createRetrievePendingMessageResponseDto);
  }

  async consumeMessage(sendMessageRequestDto: SendMessageRequestDto): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();
    logger.info(`Processing outgoing message: ${JSON.stringify(sendMessageRequestDto)}`, traceId);

    const customerPhoneEntities = await this.customerPhoneAdapter.getAll({
      customerId: sendMessageRequestDto.customerId,
      communicationChannel: sendMessageRequestDto.communicationChannel,
    });

    if (!customerPhoneEntities || customerPhoneEntities.length === 0) {
      logger.error(
        `No customer phone found. Cannot send message. CustomerId: ${sendMessageRequestDto.customerId}.
         Communication Channel: ${sendMessageRequestDto.communicationChannel}`,
        traceId,
      );
      return;
    }

    const customerPhonesDestinations =
      await this.customerPhoneDestinationAdapter.getByCustomerIdAndDestinationAndCommunicationChannel(
        sendMessageRequestDto.customerId,
        sendMessageRequestDto.to,
        sendMessageRequestDto.communicationChannel,
      );

    let customerPhoneEntity: CustomerPhoneEntity;

    if (!customerPhonesDestinations) {
      const customerPhoneNumberToUse =
        (await this.customerPhoneUseCase.getNextBalancedPhoneByCustomerIdAndCommunicationChannel(
          sendMessageRequestDto.customerId,
          sendMessageRequestDto.communicationChannel,
        )) || customerPhoneEntities[0];
      customerPhoneEntity = customerPhoneEntities.find(
        phone => phone.phoneNumber === customerPhoneNumberToUse,
      );

      const existingDeletedCustomerPhoneDestination =
        await this.customerPhoneDestinationAdapter.getDeletedByCustomerIdAndPhoneNumberAndDestinationAndCommunicationChannel(
          sendMessageRequestDto.customerId,
          customerPhoneEntity.phoneNumber,
          sendMessageRequestDto.to,
          sendMessageRequestDto.communicationChannel,
        );

      if (existingDeletedCustomerPhoneDestination) {
        logger.info(
          `Recreating deleted customer phone destination for customer: ${sendMessageRequestDto.customerId}, phone: ${customerPhoneEntity.phoneNumber}, destination: ${sendMessageRequestDto.to}, channel: ${sendMessageRequestDto.communicationChannel}`,
          traceId,
        );
        await this.customerPhoneDestinationAdapter.activate(existingDeletedCustomerPhoneDestination);
      } else {
        await this.customerPhoneDestinationAdapter.create(
          new CustomerPhoneDestinationEntity(
            sendMessageRequestDto.customerId,
            customerPhoneEntity.phoneNumber,
            sendMessageRequestDto.to,
            sendMessageRequestDto.communicationChannel,
          ),
        );
      }
    } else {
      customerPhoneEntity = customerPhoneEntities.find(
        phone => phone.phoneNumber === customerPhonesDestinations.phoneNumber,
      );
    }

    const randomDelay = this.numberService.getRandomNumber(
      Number(customerPhoneEntity.outgoingMaxDelay),
    );
    logger.debug(`Using random delay to outgoing message: ${randomDelay}`, traceId);

    let messageStatus = RecordStatus.ACTIVE;
    if (sendMessageRequestDto.isFirstMessage) {
      if (process.env.NODE_ENV === 'production') {
        messageStatus = await this.checkNumberVerification(sendMessageRequestDto, traceId);
      }
      if (messageStatus === RecordStatus.REPROCESSED) {
        await this.messageUseCase.sendMessage(sendMessageRequestDto);
      }
    }

    await this.outgoingMessageAdapter.insertOutgoingMessage(
      {
        customerId: sendMessageRequestDto.customerId,
        from: customerPhoneEntity.phoneNumber,
        to: sendMessageRequestDto.to,
        messageType: sendMessageRequestDto.messageType,
        message: sendMessageRequestDto.message,
        channel: sendMessageRequestDto.communicationChannel,
        status: messageStatus,
        isFirstMessage: sendMessageRequestDto.isFirstMessage,
        apiUrl: customerPhoneEntity.apiUrl,
        fileUrl: sendMessageRequestDto.fileUrl,
      },
      sendMessageRequestDto.isFirstMessage ? randomDelay : Math.floor(randomDelay / 4),
    );
  }

  private async checkNumberVerification(
    sendMessageRequestDto: SendMessageRequestDto,
    traceId: string,
  ): Promise<RecordStatus> {
    const [phoneVerification] = await this.phoneVerificationAdapter.getAll({
      phoneNumber: sendMessageRequestDto.to,
    });

    if (phoneVerification) {
      if (phoneVerification.verificationStatus == PhoneVerificationStatus.PENDING) {
        logger.info(
          `Number verification for: ${phoneVerification.phoneNumber} is still PENDING! Message will be reprocessed in a few seconds...`,
          traceId,
        );

        return RecordStatus.REPROCESSED;
      } else if (phoneVerification.verificationStatus == PhoneVerificationStatus.NOT_EXISTS) {
        logger.info(
          `Number verification for: ${phoneVerification.phoneNumber} is NOT_EXISTS! Reporting BUSINESS_BASE module...`,
          traceId,
        );

        const updatedItems = await this.businessBaseAdapter.updateItemStatusNumberNotExist(
          sendMessageRequestDto.to,
        );

        logger.info(
          `Updated portfolio items in BUSINESS_BASE: ${updatedItems
            .map(item => item.id)
            .join(', ')} from phone number: ${
            sendMessageRequestDto.to
          } to status NUMBER_NOT_EXISTS`,
          { traceId, updatedItems },
        );

        return RecordStatus.NUMBER_NOT_EXISTS;
      } else if (phoneVerification.verificationStatus == PhoneVerificationStatus.CONFIRMED) {
        logger.info(
          `Number verification for: ${phoneVerification.phoneNumber} is CONFIRMED! Reporting BUSINESS_BASE module...`,
          traceId,
        );

        return RecordStatus.ACTIVE;
      }
    } else {
      logger.info(
        `No phone verification found for: ${sendMessageRequestDto.to}. Creating one and reprocessing message in a few seconds...`,
        traceId,
      );
      await this.phoneVerificationUseCase.createPhonesVerification({
        customerId: sendMessageRequestDto.customerId,
        phoneNumbers: [sendMessageRequestDto.to],
      });

      return RecordStatus.REPROCESSED;
    }
  }

  async getCurrentDatabaseTimestamp(): Promise<{ now: Date; nowToString: string }> {
    return await this.outgoingMessageAdapter.getTimestampFromDatabase();
  }

  async getTotalAnswerMessagesSent(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{
    total: number;
    dailyTotals: { [date: string]: number };
  }> {
    const messages = await this.outgoingMessageAdapter.getAll({
      customerId,
      sent: true,
      isfirstMessage: false,
      sentAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
    });

    const total = messages.length;

    const dailyTotals: { [date: string]: number } = {};
    messages.forEach(message => {
      const date = new Date(message.sentAt).toISOString().split('T')[0];
      dailyTotals[date] = (dailyTotals[date] || 0) + 1;
    });

    return {
      total,
      dailyTotals,
    };
  }

  async getTotalFirstMessagesSent(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{
    total: number;
    dailyTotals: { [date: string]: number };
  }> {
    const messages = await this.outgoingMessageAdapter.getAll({
      customerId,
      sent: true,
      isfirstMessage: true,
      sentAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
    });

    const total = messages.length;

    const dailyTotals: { [date: string]: number } = {};
    messages.forEach(message => {
      const date = new Date(message.sentAt).toISOString().split('T')[0];
      dailyTotals[date] = (dailyTotals[date] || 0) + 1;
    });

    return {
      total,
      dailyTotals,
    };
  }

  async getTotalFirstMessagesSentByPortfolio(
    customerId: string,
    portfolioId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{
    total: number;
    dailyTotals: { [date: string]: number };
  }> {
    const results = await this.outgoingMessageAdapter.getTotalFirstMessagesSentByPortfolio(
      customerId,
      portfolioId,
      dateStart,
      dateEnd,
    );

    if (results.length === 0) {
      return {
        total: 0,
        dailyTotals: {},
      };
    }

    const total = results[0].total;

    const dailyTotals: { [date: string]: number } = {};
    results.forEach(row => {
      dailyTotals[row.date] = row.count;
    });

    return {
      total,
      dailyTotals,
    };
  }

  async getOutgoingMessagesByCustomerId(customerId: string, to: string): Promise<any[]> {
    logger.info(`Getting outgoing messages for customer: ${customerId}`);

    try {
      const messages = await this.outgoingMessageAdapter.getAll({
        customerId,
        to,
        status: 'ACTIVE',
      });

      const sortedMessages = messages.sort(
        (a, b) => new Date(b.timeToGo).getTime() - new Date(a.timeToGo).getTime(),
      );

      return sortedMessages.map(message => ({
        sent: message.sent,
        sent_at: message.sentAt,
        time_to_go: message.timeToGo,
        message: message.message,
        message_type: message.messageType,
        to: message.to,
        from: message.from,
        is_first_message: message.isfirstMessage,
      }));
    } catch (error) {
      logger.error(`Error getting outgoing messages for customer: ${customerId}`, error);
      throw error;
    }
  }

  async reprocessPendingMessages(customerId: string, phoneNumber: string): Promise<void> {
    logger.info(
      `Getting not sent outgoing messages for customer: ${customerId} and phone: ${phoneNumber}`,
    );
    const pendingFirstMessages = await this.outgoingMessageAdapter.getAll({
      customerId,
      from: phoneNumber,
      isfirstMessage: true,
      sent: false,
      sentAt: null,
      status: '*',
    });

    if (pendingFirstMessages.length === 0) {
      logger.info(`No pending messages found for reprocessing.`);
      return;
    }

    logger.info(`Found ${pendingFirstMessages.length} pending messages for reprocessing.`);

    for (const message of pendingFirstMessages) {
      try {
        logger.info(`Reprocessing message: ${message.id}`);

        const sendMessageRequestDto = new SendMessageRequestDto(
          message.customerId,
          message.to,
          message.messageType,
          message.channel,
          message.isfirstMessage,
          message.message,
          message.fileUrl,
        );

        await this.messageUseCase.sendMessage(sendMessageRequestDto);
        message.status = RecordStatus.REPROCESSED;
        await this.outgoingMessageAdapter.update(message);
        logger.info(
          `Successfully reprocessed message: ${message.id} to ${message.to}. From customer: ${message.customerId}`,
        );
      } catch (error) {
        logger.error(`Error reprocessing message: ${message.id}. Error: ${JSON.stringify(error)}`);
      }
    }
  }

  private createRetrievePendingMessageResponseDto(
    outgoingMessageEntity: OutgoingMessageEntity,
  ): RetrievePendingMessageResponseDto {
    return plainToClass(RetrievePendingMessageResponseDto, outgoingMessageEntity);
  }
}
